#!/usr/bin/env node

/**
 * 🧠 AUGMENT - Sistema Neural Modular
 * Ponto de entrada principal do sistema
 */

const readline = require('readline');
const path = require('path');
const fs = require('fs');

// Importar módulos do sistema
const Interface = require('./sistema/interface.js');
const Cognitivo = require('./sistema/cognitivo.js');
const Executor = require('./sistema/executor.js');

class AugmentSystem {
    constructor() {
        this.interface = new Interface();
        this.cognitivo = new Cognitivo();
        this.executor = new Executor();
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async inicializar() {
        console.log('\n🧠 AUGMENT - Sistema Neural Modular Ativo');
        console.log('═══════════════════════════════════════════');
        console.log('💡 Digite seu objetivo e eu farei engenharia reversa');
        console.log('🔄 Pressione Ctrl+C para sair\n');

        this.loop();
    }

    async loop() {
        this.rl.question('🎯 Qual é seu objetivo? ', async (input) => {
            if (input.toLowerCase() === 'sair' || input.toLowerCase() === 'exit') {
                console.log('\n👋 Sistema Augment finalizado!');
                this.rl.close();
                return;
            }

            try {
                await this.processarObjetivo(input);
            } catch (error) {
                console.error('❌ Erro:', error.message);
            }

            this.loop();
        });
    }

    async processarObjetivo(objetivo) {
        console.log('\n🔍 Analisando objetivo...');
        
        // 1. Análise cognitiva do objetivo
        const analise = await this.cognitivo.analisar(objetivo);
        console.log('📊 Análise:', analise.resumo);

        // 2. Engenharia reversa - visualizar resultado final
        const resultadoFinal = await this.cognitivo.visualizarResultado(objetivo);
        console.log('🎯 Resultado visualizado:', resultadoFinal.descricao);

        // 3. Decomposição modular
        const modulos = await this.cognitivo.decomporModulos(resultadoFinal);
        console.log('🧩 Módulos identificados:', modulos.map(m => m.nome).join(', '));

        // 4. Execução modular
        console.log('\n⚙️ Executando módulos...');
        for (const modulo of modulos) {
            await this.executor.executarModulo(modulo);
        }

        console.log('\n✅ Objetivo processado com sucesso!\n');
    }
}

// Inicializar sistema
if (require.main === module) {
    const augment = new AugmentSystem();
    augment.inicializar().catch(console.error);
}

module.exports = AugmentSystem;
