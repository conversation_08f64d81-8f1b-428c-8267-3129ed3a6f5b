{"name": "augment-sistema-neural-modular", "version": "1.0.0", "description": "Sistema de IA modular baseado em engenharia reversa que constrói soluções partindo do resultado final desejado", "main": "main.js", "scripts": {"start": "node main.js", "dev": "node --watch main.js", "test": "echo \"Testes não implementados ainda\" && exit 0", "docs": "echo \"Documentação em docs/\"", "clean": "rm -rf node_modules package-lock.json", "setup": "npm install && echo \"Sistema Augment configurado com sucesso!\""}, "keywords": ["ai", "modular", "neural", "reverse-engineering", "automation", "cognitive-architecture"], "author": "Sistema Augment", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "README.md"}