/**
 * ⚙️ EXECUTOR DE MÓDULOS
 * Responsável por executar e coordenar os módulos especializados
 */

const fs = require('fs').promises;
const path = require('path');

class ExecutorModulos {
    constructor() {
        this.modulosCarregados = new Map();
        this.resultados = [];
    }

    async executarModulo(modulo) {
        console.log(`🔧 Executando módulo: ${modulo.nome}`);
        
        try {
            // Carregar módulo se não estiver carregado
            if (!this.modulosCarregados.has(modulo.nome)) {
                await this.carregarModulo(modulo.nome);
            }

            const moduloInstance = this.modulosCarregados.get(modulo.nome);
            
            if (!moduloInstance) {
                throw new Error(`Módulo ${modulo.nome} não encontrado`);
            }

            // Executar módulo
            const resultado = await moduloInstance.executar(modulo);
            
            // Salvar resultado
            this.resultados.push({
                modulo: modulo.nome,
                resultado: resultado,
                timestamp: new Date().toISOString()
            });

            console.log(`✅ Módulo ${modulo.nome} executado com sucesso`);
            return resultado;

        } catch (error) {
            console.error(`❌ Erro ao executar módulo ${modulo.nome}:`, error.message);
            throw error;
        }
    }

    async carregarModulo(nomeModulo) {
        try {
            const moduloPath = path.join(__dirname, '..', 'modulos', `${nomeModulo}.js`);
            
            // Verificar se arquivo existe
            await fs.access(moduloPath);
            
            // Carregar módulo
            const ModuloClass = require(moduloPath);
            const moduloInstance = new ModuloClass();
            
            this.modulosCarregados.set(nomeModulo, moduloInstance);
            console.log(`📦 Módulo ${nomeModulo} carregado`);
            
        } catch (error) {
            // Se módulo não existe, criar um módulo genérico
            console.log(`⚠️ Módulo ${nomeModulo} não encontrado, criando módulo genérico`);
            this.modulosCarregados.set(nomeModulo, new ModuloGenerico(nomeModulo));
        }
    }

    async executarSequencia(modulos) {
        console.log('🔄 Executando sequência de módulos...');
        
        const resultados = [];
        
        for (const modulo of modulos) {
            const resultado = await this.executarModulo(modulo);
            resultados.push(resultado);
            
            // Pequena pausa entre execuções
            await this.pausa(500);
        }

        return resultados;
    }

    async executarParalelo(modulos) {
        console.log('⚡ Executando módulos em paralelo...');
        
        const promessas = modulos.map(modulo => this.executarModulo(modulo));
        const resultados = await Promise.all(promessas);
        
        return resultados;
    }

    obterResultados() {
        return this.resultados;
    }

    limparResultados() {
        this.resultados = [];
    }

    async pausa(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async salvarRelatorio() {
        const relatorio = {
            timestamp: new Date().toISOString(),
            modulos_executados: this.resultados.length,
            resultados: this.resultados,
            resumo: this.gerarResumo()
        };

        const relatorioPath = path.join(__dirname, '..', 'docs', 'ultimo_relatorio.json');
        await fs.writeFile(relatorioPath, JSON.stringify(relatorio, null, 2));
        
        console.log('📊 Relatório salvo em docs/ultimo_relatorio.json');
        return relatorio;
    }

    gerarResumo() {
        const modulosUnicos = [...new Set(this.resultados.map(r => r.modulo))];
        const sucessos = this.resultados.filter(r => r.resultado.sucesso).length;
        const falhas = this.resultados.length - sucessos;

        return {
            modulos_utilizados: modulosUnicos,
            total_execucoes: this.resultados.length,
            sucessos: sucessos,
            falhas: falhas,
            taxa_sucesso: `${((sucessos / this.resultados.length) * 100).toFixed(1)}%`
        };
    }
}

/**
 * Módulo genérico para casos onde o módulo específico não existe
 */
class ModuloGenerico {
    constructor(nome) {
        this.nome = nome;
    }

    async executar(modulo) {
        console.log(`🔧 Executando módulo genérico: ${this.nome}`);
        
        // Simular processamento
        await this.pausa(1000);
        
        const resultado = {
            sucesso: true,
            modulo: this.nome,
            descricao: modulo.descricao,
            saida: `Módulo ${this.nome} processado com sucesso`,
            dados: {
                tipo: 'generico',
                processado_em: new Date().toISOString()
            }
        };

        return resultado;
    }

    async pausa(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ExecutorModulos;
