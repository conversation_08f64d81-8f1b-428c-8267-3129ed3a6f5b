/**
 * 🖥️ INTERFACE PRINCIPAL
 * Responsável pela comunicação com o usuário e apresentação de resultados
 */

const readline = require('readline');

class InterfacePrincipal {
    constructor() {
        this.historico = [];
        this.configuracoes = {
            cores: true,
            verbose: false,
            salvar_historico: true
        };
    }

    // Métodos de saída formatada
    log(mensagem, tipo = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        let prefixo = '';
        let cor = 'white';

        switch (tipo) {
            case 'sucesso':
                prefixo = '✅';
                cor = 'green';
                break;
            case 'erro':
                prefixo = '❌';
                cor = 'red';
                break;
            case 'aviso':
                prefixo = '⚠️';
                cor = 'yellow';
                break;
            case 'info':
                prefixo = 'ℹ️';
                cor = 'blue';
                break;
            case 'processo':
                prefixo = '⚙️';
                cor = 'cyan';
                break;
            default:
                prefixo = '📝';
        }

        const mensagemFormatada = `[${timestamp}] ${prefixo} ${mensagem}`;

        if (this.configuracoes.cores && chalkLib) {
            console.log(chalkLib[cor](mensagemFormatada));
        } else {
            console.log(mensagemFormatada);
        }

        // Salvar no histórico
        if (this.configuracoes.salvar_historico) {
            this.historico.push({
                timestamp: new Date().toISOString(),
                tipo: tipo,
                mensagem: mensagem
            });
        }
    }

    sucesso(mensagem) {
        this.log(mensagem, 'sucesso');
    }

    erro(mensagem) {
        this.log(mensagem, 'erro');
    }

    aviso(mensagem) {
        this.log(mensagem, 'aviso');
    }

    info(mensagem) {
        this.log(mensagem, 'info');
    }

    processo(mensagem) {
        this.log(mensagem, 'processo');
    }

    // Métodos de entrada
    async pergunta(texto) {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            rl.question(texto, (resposta) => {
                rl.close();
                resolve(resposta.trim());
            });
        });
    }

    async confirmar(texto) {
        const resposta = await this.pergunta(`${texto} (s/n): `);
        return resposta.toLowerCase() === 's' || resposta.toLowerCase() === 'sim';
    }

    async escolherOpcao(texto, opcoes) {
        console.log(`\n${texto}`);
        opcoes.forEach((opcao, index) => {
            console.log(`${index + 1}. ${opcao}`);
        });

        const resposta = await this.pergunta('\nEscolha uma opção (número): ');
        const indice = parseInt(resposta) - 1;

        if (indice >= 0 && indice < opcoes.length) {
            return { indice, opcao: opcoes[indice] };
        } else {
            this.erro('Opção inválida');
            return await this.escolherOpcao(texto, opcoes);
        }
    }

    // Métodos de apresentação
    exibirTitulo(titulo) {
        const linha = '═'.repeat(titulo.length + 4);
        console.log(`\n${linha}`);
        console.log(`  ${titulo}  `);
        console.log(`${linha}\n`);
    }

    exibirSecao(titulo) {
        console.log(`\n🔸 ${titulo}`);
        console.log('─'.repeat(titulo.length + 3));
    }

    exibirLista(items, titulo = null) {
        if (titulo) {
            this.exibirSecao(titulo);
        }

        items.forEach((item, index) => {
            if (typeof item === 'string') {
                console.log(`  ${index + 1}. ${item}`);
            } else {
                console.log(`  ${index + 1}. ${item.nome || item.titulo}: ${item.descricao || item.valor}`);
            }
        });
    }

    exibirTabela(dados, colunas) {
        if (!dados || dados.length === 0) {
            this.aviso('Nenhum dado para exibir');
            return;
        }

        // Cabeçalho
        const cabecalho = colunas.map(col => col.padEnd(15)).join(' | ');
        console.log(`\n${cabecalho}`);
        console.log('─'.repeat(cabecalho.length));

        // Dados
        dados.forEach(linha => {
            const linhaDados = colunas.map(col => {
                const valor = linha[col] || '';
                return String(valor).padEnd(15);
            }).join(' | ');
            console.log(linhaDados);
        });
    }

    exibirProgresso(atual, total, descricao = '') {
        const porcentagem = Math.round((atual / total) * 100);
        const barraCompleta = 20;
        const preenchido = Math.round((porcentagem / 100) * barraCompleta);
        const vazio = barraCompleta - preenchido;

        const barra = '█'.repeat(preenchido) + '░'.repeat(vazio);
        const texto = `[${barra}] ${porcentagem}% ${descricao}`;

        // Limpar linha anterior e escrever nova
        process.stdout.write('\r' + texto);

        if (atual === total) {
            console.log(); // Nova linha quando completo
        }
    }

    // Métodos de configuração
    configurar(opcoes) {
        this.configuracoes = { ...this.configuracoes, ...opcoes };
    }

    ativarVerbose() {
        this.configuracoes.verbose = true;
        this.info('Modo verbose ativado');
    }

    desativarCores() {
        this.configuracoes.cores = false;
        this.info('Cores desativadas');
    }

    // Métodos de histórico
    obterHistorico() {
        return this.historico;
    }

    limparHistorico() {
        this.historico = [];
        this.info('Histórico limpo');
    }

    salvarHistorico(arquivo = 'historico.json') {
        const fs = require('fs').promises;
        const path = require('path');

        const dados = {
            timestamp: new Date().toISOString(),
            total_entradas: this.historico.length,
            historico: this.historico
        };

        const caminhoArquivo = path.join(__dirname, '..', 'docs', arquivo);

        return fs.writeFile(caminhoArquivo, JSON.stringify(dados, null, 2))
            .then(() => {
                this.sucesso(`Histórico salvo em ${arquivo}`);
            })
            .catch(erro => {
                this.erro(`Erro ao salvar histórico: ${erro.message}`);
            });
    }

    // Método para exibir ajuda
    exibirAjuda() {
        this.exibirTitulo('AJUDA - Sistema Augment');

        console.log('Comandos disponíveis:');
        console.log('  help, ajuda     - Exibe esta ajuda');
        console.log('  clear, limpar   - Limpa a tela');
        console.log('  historico       - Exibe histórico de comandos');
        console.log('  config          - Exibe configurações atuais');
        console.log('  verbose         - Ativa/desativa modo verbose');
        console.log('  sair, exit      - Sai do sistema');
        console.log('\nPara usar o sistema, digite seu objetivo e pressione Enter.');
    }
}

// Tentar carregar chalk para cores, mas funcionar sem ele
let chalkLib;
try {
    chalkLib = require('chalk');
} catch (error) {
    // chalk não está disponível, usar console.log normal
    chalkLib = null;
}

module.exports = InterfacePrincipal;
