#!/usr/bin/env node

/**
 * 🧪 TESTE SIMPLES DO SISTEMA AUGMENT
 */

console.log('🧠 Testando Sistema Augment...');

try {
    // Testar importação dos módulos
    console.log('📦 Carregando módulos...');
    
    const Cognitivo = require('./sistema/cognitivo.js');
    console.log('✅ Módulo Cognitivo carregado');
    
    const Executor = require('./sistema/executor.js');
    console.log('✅ Módulo Executor carregado');
    
    const Interface = require('./sistema/interface.js');
    console.log('✅ Módulo Interface carregado');
    
    // Teste básico do núcleo cognitivo
    console.log('\n🧠 Testando núcleo cognitivo...');
    const cognitivo = new Cognitivo();
    
    // Teste de análise simples
    const objetivo = "criar um site simples";
    console.log(`🎯 Objetivo de teste: "${objetivo}"`);
    
    cognitivo.analisar(objetivo).then(analise => {
        console.log('✅ Análise concluída:');
        console.log(`   Intenção: ${analise.intencao.acao}`);
        console.log(`   Contexto: ${analise.contexto.dominio}`);
        console.log(`   Complexidade: ${analise.complexidade.nivel}`);
        
        console.log('\n🎉 Sistema Augment funcionando corretamente!');
        console.log('💡 Execute "node main.js" para usar o sistema completo');
        
    }).catch(error => {
        console.error('❌ Erro no teste:', error.message);
    });
    
} catch (error) {
    console.error('❌ Erro ao carregar módulos:', error.message);
    console.error('📋 Stack:', error.stack);
}
